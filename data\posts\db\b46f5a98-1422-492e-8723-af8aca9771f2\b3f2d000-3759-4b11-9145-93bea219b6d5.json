{"id": "b3f2d000-3759-4b11-9145-93bea219b6d5", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "test", "hashtagCount": 3, "imageSize": "Auto", "quality": "BALANCED", "postId": null, "captionPart": "Conducting routine system tests to ensure optimal performance and data integrity. Proactive testing is crucial for maintaining reliable operations and safeguarding against potential issues. Continuous quality assurance is a priority. #QualityAssurance #SystemTesting #DataIntegrity", "hashtagsPart": "#QualityAssurance #SystemTesting #DataIntegrity"}, "caption": "Conducting routine system tests to ensure optimal performance and data integrity. Proactive testing is crucial for maintaining reliable operations and safeguarding against potential issues. Continuous quality assurance is a priority. #QualityAssurance #SystemTesting #DataIntegrity\n\n#QualityAssurance #SystemTesting #DataIntegrity", "captionPart": "Conducting routine system tests to ensure optimal performance and data integrity. Proactive testing is crucial for maintaining reliable operations and safeguarding against potential issues. Continuous quality assurance is a priority. #QualityAssurance #SystemTesting #DataIntegrity", "hashtagsPart": "#QualityAssurance #SystemTesting #DataIntegrity", "imageUrl": "/api/images/55f6ab62-f2ae-44a8-9851-ca885d6d24dd", "createdAt": "2025-05-25T08:41:08.167Z", "updatedAt": "2025-05-25T08:41:14.971Z"}