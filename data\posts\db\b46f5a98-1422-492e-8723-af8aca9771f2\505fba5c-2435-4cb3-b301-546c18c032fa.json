{"id": "505fba5c-2435-4cb3-b301-546c18c032fa", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "test", "hashtagCount": 3, "imageSize": "1:1", "quality": "BALANCED", "captionPart": "Conducting routine system tests to ensure optimal performance and reliability. A proactive approach to maintaining a stable and efficient workflow. Quality assurance is a cornerstone of our operations. #QualityAssurance #SystemTesting #ProcessOptimization", "hashtagsPart": "#QualityAssurance #SystemTesting #ProcessOptimization", "isGenerating": false}, "caption": "Conducting routine system tests to ensure optimal performance and reliability. A proactive approach to maintaining a stable and efficient workflow. Quality assurance is a cornerstone of our operations. #QualityAssurance #SystemTesting #ProcessOptimization\n\n#QualityAssurance #SystemTesting #ProcessOptimization", "captionPart": "Conducting routine system tests to ensure optimal performance and reliability. A proactive approach to maintaining a stable and efficient workflow. Quality assurance is a cornerstone of our operations. #QualityAssurance #SystemTesting #ProcessOptimization", "hashtagsPart": "#QualityAssurance #SystemTesting #ProcessOptimization", "imageUrl": null, "createdAt": "2025-05-22T16:05:57.479Z", "updatedAt": "2025-05-22T16:06:00.242Z"}