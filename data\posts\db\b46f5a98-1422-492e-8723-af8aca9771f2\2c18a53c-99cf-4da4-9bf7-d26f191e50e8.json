{"id": "2c18a53c-99cf-4da4-9bf7-d26f191e50e8", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "test", "hashtagCount": 3, "imageSize": "Auto", "quality": "BALANCED", "postId": null, "captionPart": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and a seamless user experience. We're committed to rigorous quality assurance across all platforms. #QualityAssurance #SystemTesting #TechReliability", "hashtagsPart": "#QualityAssurance #SystemTesting #TechReliability"}, "caption": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and a seamless user experience. We're committed to rigorous quality assurance across all platforms. #QualityAssurance #SystemTesting #TechReliability\n\n#QualityAssurance #SystemTesting #TechReliability", "captionPart": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and a seamless user experience. We're committed to rigorous quality assurance across all platforms. #QualityAssurance #SystemTesting #TechReliability", "hashtagsPart": "#QualityAssurance #SystemTesting #TechReliability", "imageUrl": "/api/images/a0c884ec-72ed-4791-ac41-c304e50ba156", "createdAt": "2025-05-23T19:51:40.472Z", "updatedAt": "2025-05-23T19:51:48.946Z"}