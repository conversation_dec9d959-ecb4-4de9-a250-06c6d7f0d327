{"id": "b3f5664c-853a-45a3-b348-83a2a4855c6d", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "test", "hashtagCount": 3, "imageSize": "Auto", "quality": "BALANCED", "postId": null, "captionPart": "Conducting routine system tests to ensure optimal performance and reliability. Proactive testing is crucial for maintaining high standards and minimizing potential disruptions. We're committed to delivering consistent, dependable solutions. #QualityAssurance #SystemTesting #Reliability", "hashtagsPart": "#QualityAssurance #SystemTesting #Reliability"}, "caption": "Conducting routine system tests to ensure optimal performance and reliability. Proactive testing is crucial for maintaining high standards and minimizing potential disruptions. We're committed to delivering consistent, dependable solutions. #QualityAssurance #SystemTesting #Reliability\n\n#QualityAssurance #SystemTesting #Reliability", "captionPart": "Conducting routine system tests to ensure optimal performance and reliability. Proactive testing is crucial for maintaining high standards and minimizing potential disruptions. We're committed to delivering consistent, dependable solutions. #QualityAssurance #SystemTesting #Reliability", "hashtagsPart": "#QualityAssurance #SystemTesting #Reliability", "imageUrl": "/api/images/847807ed-173d-4c57-b52e-8c9aa43c828e", "createdAt": "2025-05-23T12:38:47.387Z", "updatedAt": "2025-05-23T12:38:53.695Z"}