{"id": "d12f6e83-cef8-4f25-8abf-ac970ef92b82", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "test", "hashtagCount": 3, "imageSize": "Auto", "quality": "BALANCED", "postId": null, "captionPart": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and minimizing potential disruptions. A robust testing framework is a cornerstone of reliable operations. #QualityAssurance #SystemTesting #OperationalExcellence", "hashtagsPart": "#QualityAssurance #SystemTesting #OperationalExcellence"}, "caption": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and minimizing potential disruptions. A robust testing framework is a cornerstone of reliable operations. #QualityAssurance #SystemTesting #OperationalExcellence\n\n#QualityAssurance #SystemTesting #OperationalExcellence", "captionPart": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and minimizing potential disruptions. A robust testing framework is a cornerstone of reliable operations. #QualityAssurance #SystemTesting #OperationalExcellence", "hashtagsPart": "#QualityAssurance #SystemTesting #OperationalExcellence", "imageUrl": "/api/images/9d03466b-e0bd-4f5d-836c-2c9ebcd3b7a0", "createdAt": "2025-05-23T20:27:40.602Z", "updatedAt": "2025-05-23T20:27:54.684Z"}