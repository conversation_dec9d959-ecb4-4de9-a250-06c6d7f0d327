{"id": "ad12ca12-8ff1-4682-982b-4741b684acfe", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "asdsad", "hashtagCount": 3, "imageSize": "Auto", "quality": "BALANCED", "postId": null, "captionPart": "Navigating complex challenges often requires a structured approach. We're focusing on strategic problem-solving and innovative solutions to achieve impactful results. Stay tuned for updates on our progress! #StrategicThinking #ProblemSolving #Innovation", "hashtagsPart": "#StrategicThinking #ProblemSolving #Innovation"}, "caption": "Navigating complex challenges often requires a structured approach. We're focusing on strategic problem-solving and innovative solutions to achieve impactful results. Stay tuned for updates on our progress! #StrategicThinking #ProblemSolving #Innovation\n\n#StrategicThinking #ProblemSolving #Innovation", "captionPart": "Navigating complex challenges often requires a structured approach. We're focusing on strategic problem-solving and innovative solutions to achieve impactful results. Stay tuned for updates on our progress! #StrategicThinking #ProblemSolving #Innovation", "hashtagsPart": "#StrategicThinking #ProblemSolving #Innovation", "imageUrl": "/api/images/c240b23a-522d-4055-a761-c78d582770bb", "createdAt": "2025-05-24T12:18:40.923Z", "updatedAt": "2025-05-24T12:18:48.833Z"}