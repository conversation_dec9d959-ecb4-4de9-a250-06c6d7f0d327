{"id": "d0ac0b20-cac9-40ea-8427-200e643d69ce", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "test", "hashtagCount": 3, "imageSize": "1:1", "quality": "BALANCED", "postId": null, "captionPart": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and delivering a seamless user experience. We're committed to quality and continuous improvement. #QualityAssurance #SystemTesting #DataIntegrity", "hashtagsPart": "#QualityAssurance #SystemTesting #DataIntegrity"}, "caption": "", "captionPart": "Conducting routine system tests to ensure optimal performance and stability. Proactive testing is crucial for maintaining data integrity and delivering a seamless user experience. We're committed to quality and continuous improvement. #QualityAssurance #SystemTesting #DataIntegrity", "hashtagsPart": "#QualityAssurance #SystemTesting #DataIntegrity", "imageUrl": "/api/images/4f0dfa7e-03a7-4fbe-b341-949adb4835c4", "createdAt": "2025-05-22T16:10:07.319Z", "updatedAt": "2025-05-22T16:10:07.319Z"}