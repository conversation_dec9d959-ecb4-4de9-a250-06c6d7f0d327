{"id": "ac534541-f4a6-4c33-825e-0397067778d0", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "test", "hashtagCount": 3, "imageSize": "1:1", "quality": "FAST", "postId": null, "captionPart": "Thorough testing is crucial to ensuring the quality and reliability of any product or service. Whether it's software, hardware, or a new process, rigorous testing identifies potential issues early, leading to enhanced performance and a better user experience. We prioritize testing at every stage! #QualityAssurance #Testing #SoftwareTesting", "hashtagsPart": "#QualityAssurance #Testing #SoftwareTesting"}, "caption": "Thorough testing is crucial to ensuring the quality and reliability of any product or service. Whether it's software, hardware, or a new process, rigorous testing identifies potential issues early, leading to enhanced performance and a better user experience. We prioritize testing at every stage! #QualityAssurance #Testing #SoftwareTesting\n\n#QualityAssurance #Testing #SoftwareTesting", "captionPart": "Thorough testing is crucial to ensuring the quality and reliability of any product or service. Whether it's software, hardware, or a new process, rigorous testing identifies potential issues early, leading to enhanced performance and a better user experience. We prioritize testing at every stage! #QualityAssurance #Testing #SoftwareTesting", "hashtagsPart": "#QualityAssurance #Testing #SoftwareTesting", "imageUrl": "/api/images/dec65c2b-f020-4d79-a5d1-a6d7761817bf", "createdAt": "2025-05-22T16:15:24.812Z", "updatedAt": "2025-05-22T16:15:33.400Z"}