{"id": "736cacbd-96df-440a-9e5d-9efebeb3b7a9", "userId": "b46f5a98-1422-492e-8723-af8aca9771f2", "content": {"platform": "Instagram", "tone": "Professional", "topic": "I want you to make ads for SocialSparkAI this is the platform I'm currently using to generate this post, it allow users to generate social media post with text and images using only simple idea, with free plan and premium option", "hashtagCount": 3, "imageSize": "Auto", "quality": "QUALITY", "postId": null, "captionPart": "Elevate your social media presence with ease. This post was generated using SocialSparkGen, a powerful platform designed to transform your simple ideas into compelling social media content, complete with text and images. Streamline your content creation workflow and explore the possibilities with our free plan or unlock advanced features with premium options. Discover how SocialSparkGen can empower your brand's voice.", "hashtagsPart": "#AItools #SocialMediaMarketing #ContentCreation"}, "caption": "Elevate your social media presence with ease. This post was generated using SocialSparkGen, a powerful platform designed to transform your simple ideas into compelling social media content, complete with text and images. Streamline your content creation workflow and explore the possibilities with our free plan or unlock advanced features with premium options. Discover how SocialSparkGen can empower your brand's voice.\n\n#AItools #SocialMediaMarketing #ContentCreation", "captionPart": "Elevate your social media presence with ease. This post was generated using SocialSparkGen, a powerful platform designed to transform your simple ideas into compelling social media content, complete with text and images. Streamline your content creation workflow and explore the possibilities with our free plan or unlock advanced features with premium options. Discover how SocialSparkGen can empower your brand's voice.", "hashtagsPart": "#AItools #SocialMediaMarketing #ContentCreation", "imageUrl": "/api/images/a8312a86-a85e-44d6-ab4b-eb25c559f5e5", "createdAt": "2025-05-24T10:35:49.356Z", "updatedAt": "2025-05-24T10:36:00.706Z"}